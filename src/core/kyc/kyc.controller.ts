import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Query,
  Post,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { KycService } from './kyc.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { CreateKycDto } from './dto/create.dto';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import { PaginationQuery } from 'src/common/interfaces';
import { User } from 'src/common/decorators';

@ApiTags('KYC')
@UseGuards(JwtAuthGuard)
@Controller('kyc')
export class KycController {
  constructor(private readonly kycService: KycService) {}

  @Post()
  @ApiOperation({ summary: 'Create new KYC record' })
  @ApiResponse({ status: 201, description: 'KYC record created successfully' })
  @ApiResponse({
    status: 400,
    description: 'Bad request or KYC already exists',
  })
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'frontId', maxCount: 1 },
      { name: 'backId', maxCount: 1 },
      { name: 'selfie', maxCount: 1 },
    ]),
  )
  async createKyc(
    @Body() dto: CreateKycDto,
    @UploadedFiles()
    files: {
      frontId?: Express.Multer.File[];
      backId?: Express.Multer.File[];
      selfie?: Express.Multer.File[];
    },
  ) {
    // return this.kycService.create(dto, files);
  }

  @Get()
  @ApiOperation({ summary: 'Get all KYC records' })
  @ApiResponse({ status: 200, description: 'Returns list of KYC records' })
  async getAllKyc(@Query() query: PaginationQuery) {
    return this.kycService.getAll(query);
  }

  @Get('user')
  @ApiOperation({ summary: 'Get KYC record for current user' })
  @ApiResponse({ status: 200, description: 'Returns user KYC record' })
  @ApiResponse({ status: 404, description: 'KYC not found' })
  async getUserKyc(@User('id') userId: number) {
    return this.kycService.findByUserId(userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete KYC record' })
  @ApiResponse({ status: 200, description: 'KYC record deleted successfully' })
  @ApiResponse({ status: 404, description: 'KYC not found' })
  async deleteKyc(@Param('id', ParseIntPipe) id: number) {
    return this.kycService.delete(id);
  }
}
