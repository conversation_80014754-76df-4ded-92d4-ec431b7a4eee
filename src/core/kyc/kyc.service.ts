/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { EmailsService } from '../emails/emails.service';
import { FilesService } from '../files/files.service';
import {
  FileRepository,
  KycRepository,
  UserRepository,
} from './../../common/repository';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { CreateKycDto } from './dto/create.dto';
import { PaginationQuery } from 'src/common/interfaces';

@Injectable()
export class KycService {
  constructor(
    private readonly kycRepository: KycRepository,
    private readonly emailService: EmailsService,
    private readonly fileService: FilesService,
    private readonly usersRepository: UserRepository,
    private readonly fileRepository: FileRepository,
  ) {}

  async create(
    userId: number,
    dto: CreateKycDto,
    files?: Express.Multer.File[],
  ) {
    try {
      const user = await this.usersRepository.findById(userId);
      if (!user) throw new BadRequestException('User not found');
      const existingKyc = await this.kycRepository.findByUserId(userId);
      if (existingKyc)
        throw new BadRequestException('KYC already exists for this user');

      const kyc = await this.kycRepository.create({
        userId,
        country: dto.country,
        dob: dto.dob,
        phoneNumber: dto.phoneNumber,
        address: dto.address,
        status: 'pending',
      });

      return kyc;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getAll(query: PaginationQuery) {
    try {
      return await this.kycRepository.getAll(query);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async findByUserId(userId: number) {
    try {
      const kyc = await this.kycRepository.findByUserId(userId);
      if (!kyc) throw new BadRequestException('KYC not found');
      return kyc;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async delete(id: number) {
    try {
      const deleted = await this.kycRepository.delete(id);
      if (!deleted) throw new BadRequestException('Failed to delete KYC');
      return { message: 'KYC deleted successfully' };
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
